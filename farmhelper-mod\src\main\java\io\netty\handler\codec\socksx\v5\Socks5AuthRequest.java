/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package io.netty.handler.codec.socksx.v5;

import io.netty.buffer.ByteBuf;
import io.netty.util.CharsetUtil;

import java.nio.charset.CharsetEncoder;

/**
 * An socks auth request.
 *
 * @see Socks5AuthResponse
 * @see Socks5AuthRequestDecoder
 */
public final class Socks5AuthRequest extends Socks5Request {
    private static final CharsetEncoder asciiEncoder = CharsetUtil.getEncoder(CharsetUtil.US_ASCII);
    private static final Socks5SubnegotiationVersion SUBNEGOTIATION_VERSION =
            Socks5SubnegotiationVersion.AUTH_PASSWORD;
    private final String username;
    private final String password;

    public Socks5AuthRequest(String username, String password) {
        super(Socks5RequestType.AUTH);
        if (username == null) {
            throw new NullPointerException("username");
        }
        if (password == null) {
            throw new NullPointerException("username");
        }
        if (!asciiEncoder.canEncode(username) || !asciiEncoder.canEncode(password)) {
            throw new IllegalArgumentException(" username: " + username + " or password: " + password +
                    " values should be in pure ascii");
        }
        if (username.length() > 255) {
            throw new IllegalArgumentException(username + " exceeds 255 char limit");
        }
        if (password.length() > 255) {
            throw new IllegalArgumentException(password + " exceeds 255 char limit");
        }
        this.username = username;
        this.password = password;
    }

    /**
     * Returns username that needs to be authenticated
     *
     * @return username that needs to be authenticated
     */
    public String username() {
        return username;
    }

    /**
     * Returns password that needs to be validated
     *
     * @return password that needs to be validated
     */
    public String password() {
        return password;
    }

    @Override
    void encodeAsByteBuf(ByteBuf byteBuf) {
        byteBuf.writeByte(SUBNEGOTIATION_VERSION.byteValue());
        byteBuf.writeByte(username.length());
        byteBuf.writeBytes(username.getBytes(CharsetUtil.US_ASCII));
        byteBuf.writeByte(password.length());
        byteBuf.writeBytes(password.getBytes(CharsetUtil.US_ASCII));
    }
}
