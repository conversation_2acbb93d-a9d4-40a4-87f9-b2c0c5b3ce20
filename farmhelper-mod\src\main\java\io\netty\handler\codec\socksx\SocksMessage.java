/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package io.netty.handler.codec.socksx;

/**
 * An abstract class that defines a SocksMessage, providing common properties for
 * {@link SocksRequest} and {@link SocksResponse}.
 */
public abstract class SocksMessage {
    private final SocksMessageType type;
    private final SocksProtocolVersion protocolVersion;

    protected SocksMessage(SocksProtocolVersion protocolVersion, SocksMessageType type) {
        if (protocolVersion == null) {
            throw new NullPointerException("protocolVersion");
        }
        if (type == null) {
            throw new NullPointerException("type");
        }
        this.protocolVersion = protocolVersion;
        this.type = type;
    }

    /**
     * Returns the {@link SocksMessageType} of this {@link SocksMessage}
     *
     * @return The {@link SocksMessageType} of this {@link SocksMessage}
     */
    public SocksMessageType type() {
        return type;
    }

    /**
     * Returns the {@link SocksProtocolVersion} of this {@link SocksMessage}
     *
     * @return The {@link SocksProtocolVersion} of this {@link SocksMessage}
     */
    public SocksProtocolVersion protocolVersion() {
        return protocolVersion;
    }
}
