subprojects {
    apply plugin: "java"

    sourceCompatibility = targetCompatibility = JavaVersion.VERSION_1_8
    archivesBaseName = project.name
    version = project.version
    // copy artifacts to root build folder after build
    tasks.getByName("build").doLast {
        String version = project.name == "farmhelper-bot" ? project.botversion : project.modversion
        copy {
            print("${project.rootProject.rootDir}/${project.name}/build/libs/${project.archivesBaseName}-${version}.jar")
            from "${project.rootProject.rootDir}/${project.name}/build/libs/${project.archivesBaseName}-${version}.jar"
            into "${project.rootProject.rootDir}/build"
        }
    }
}