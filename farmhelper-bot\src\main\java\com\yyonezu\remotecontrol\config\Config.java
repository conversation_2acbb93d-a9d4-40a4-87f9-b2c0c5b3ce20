package com.yyonezu.remotecontrol.config;

import com.yyonezu.remotecontrol.config.interfaces.SecretConfig;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

public class Config {
    private static JSONObject config;
    private static final File configFile = new File("botconfig.json");

    public static void init() {
        // Create config file if it doesn't exist
        if (!configFile.isFile()) {
            writeConfig(DefaultConfig.getDefaultConfig());
            System.out.println("Config file not found. Please create botconfig.json with token and password fields.");
            System.out.println("Example: {\"token\":\"your_discord_token\", \"password\":\"your_password\"}");
            System.exit(1);
        }

        try (FileReader reader = new FileReader("botconfig.json")) {
            config = (JSONObject) new JSONParser().parse(reader);
            updateInterfaces();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        updateInterfaces();

        // if it's still not there
        if (SecretConfig.password.length() == 0) {
            System.out.println("Password not set in config. Please set password in botconfig.json");
            System.exit(1);
        }
    }

    private static void writeConfig(JSONObject json) {
        try (FileWriter file = new FileWriter("botconfig.json")) {
            file.write(json.toString());
            file.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void updateInterfaces() {
        SecretConfig.update();
    }

    public static Object get(String property) {
        if (config.get(property) == null) {
            set(property, DefaultConfig.getDefaultConfig().get(property));
        }
        return config.get(property);
    }

    public static void set(String property, Object value) {
        config.put(property, value);
        writeConfig(config);
        Config.updateInterfaces();
    }
}
