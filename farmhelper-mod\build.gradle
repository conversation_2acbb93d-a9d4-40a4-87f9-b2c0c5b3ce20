buildscript {
    ext.kotlin_version = "1.6.10"

    repositories {
        jcenter()
        maven {
            name = "forge"
            url = "https://files.minecraftforge.net/maven"
        }
        maven {
            url "https://jitpack.io"
        }
        maven {
            url "https://repo.spongepowered.org/repository/maven-public/"
        }
        maven {
            url = "https://pkgs.dev.azure.com/djtheredstoner/DevAuth/_packaging/public/maven/v1"
        }
        maven {
            url 'https://oss.sonatype.org/content/repositories/'
        }
    }
    dependencies {
        classpath "com.github.Skytils:ForgeGradle:41dfce0a70"
        classpath "com.github.jengelman.gradle.plugins:shadow:6.1.0"
        classpath "com.github.xcfrg:MixinGradle:0.6-SNAPSHOT"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.dorkbox:Notify:3.7"
    }
}

apply plugin: 'kotlin'
apply plugin: "net.minecraftforge.gradle.forge"
apply plugin: "com.github.johnrengelman.shadow"
apply plugin: "org.spongepowered.mixin"


version = project.modversion
botversion = project.botversion

group = "com.jelly.farmhelper"
archivesBaseName = "FarmHelper"

compileJava.options.encoding = 'UTF-8'
sourceCompatibility = targetCompatibility = compileJava.sourceCompatibility = compileJava.targetCompatibility = '1.8'

minecraft {
    version = "1.8.9-11.15.1.2318-1.8.9"
    runDir = "farmhelper-mod/run"
    mappings = "stable_22"
    makeObfSourceJar = false

    clientRunArgs += "--mixin mixins.farmhelper.json"
    clientRunArgs += '--tweakClass gg.essential.loader.stage0.EssentialSetupTweaker'
}


configurations {
    include
    implementation.extendsFrom(include)
}

repositories {
    maven {
        url = "https://repo.sk1er.club/repository/maven-public"
    }
    maven {
        url = "https://jitpack.io/"
    }
    maven {
        url = "https://repo.spongepowered.org/repository/maven-public/"
    }
    mavenCentral()
    maven {
        url = "https://pkgs.dev.azure.com/djtheredstoner/DevAuth/_packaging/public/maven/v1"
    }
}

dependencies {
    implementation('org.spongepowered:mixin:0.7.11-SNAPSHOT')
    annotationProcessor('org.spongepowered:mixin:0.7.11-SNAPSHOT')

    implementation "gg.essential:essential-1.8.9-forge:1725"
    include "gg.essential:loader-launchwrapper:1.1.3"

    include('com.github.RewisServer:brigadier:master-SNAPSHOT')
    include('com.github.ronmamo:reflections:master-SNAPSHOT')

    include("org.java-websocket:Java-WebSocket:1.5.3")

    include ("com.dorkbox:Notify:3.7")
    implementation ("com.dorkbox:Notify:3.7")

    include('com.googlecode.json-simple:json-simple:1.1.1')
    implementation('com.googlecode.json-simple:json-simple:1.1.1')

    implementation('org.projectlombok:lombok:1.18.24')
    annotationProcessor('org.projectlombok:lombok:1.18.24')

    implementation("me.djtheredstoner:DevAuth-forge-legacy:1.1.0")
}

mixin {
    add sourceSets.main, "mixins.farmhelper.refmap.json"
    defaultObfuscationEnv searge
}

jar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE

    manifest.attributes(
            "ForceLoadAsMod": true,
            "ModSide": "CLIENT",
            "TweakClass": "gg.essential.loader.stage0.EssentialSetupTweaker",
            "TweakOrder": "0",
            "MixinConfigs": "mixins.farmhelper.json",
            "modversion": version,
            "botversion": botversion,
    )

    enabled = false
}

sourceJar {
    enabled = false

}

shadowJar {
    archiveClassifier.set("")
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    configurations = [project.configurations.include]

    exclude "LICENSE.md"
    exclude "pack.mcmeta"
    exclude "dummyThing"
    exclude "**/module-info.class"
    exclude "*.so"
    exclude "*.dylib"
    exclude "*.dll"
    exclude "*.jnilib"
    exclude "ibxm/**"
    exclude "com/jcraft/**"
    exclude "org/lwjgl/**"
    exclude "net/java/**"

    exclude "META-INF/proguard/**"
    exclude "META-INF/maven/**"
    exclude "META-INF/versions/**"
    exclude "META-INF/com.android.tools/**"

    exclude "fabric.mod.json"
}

tasks.reobfJar.dependsOn tasks.shadowJar

reobf {
    shadowJar {
        classpath = sourceSets.main.compileClasspath
    }
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    filesMatching("mcmod.info") {
        expand(
                "version": project.version,
                "mcversion": project.minecraft.version
        )
    }
}