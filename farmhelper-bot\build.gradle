plugins {
    id 'java'
    id 'application'
    id 'com.github.johnrengelman.shadow' version '6.1.0'
}

version = project.botversion
modversion = project.modversion
group = "com.yyonezu.remotecontrol"
archivesBaseName = "FarmHelper-Bot"
sourceCompatibility = 1.8


def mainClass = 'com.yyonezu.remotecontrol.Main'

run {
    mainClassName = mainClass
}

shadowJar {
    classifier = null
    project.configurations.implementation.canBeResolved(true)
    configurations = [project.configurations.implementation]
}

jar {
    manifest {
        attributes(
                "botversion": version,
                "modversion": modversion
        )
    }
    enabled = true
}

repositories {
    mavenCentral()
    maven {
        name 'm2-dv8tion'
        url 'https://m2.dv8tion.net/releases'
    }
    maven { url 'https://jitpack.io' }
    maven { url "https://m2.chew.pro/releases" }
}

dependencies {
    implementation("net.dv8tion:JDA:5.0.0-alpha.12")
    implementation('com.github.Kaktushose:jda-commands:v3.0.0')
    implementation('io.javalin:javalin:4.6.3')
    implementation('net.jodah:typetools:0.6.3')
    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    testCompileOnly 'org.projectlombok:lombok:1.18.24'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.24'
    implementation('com.googlecode.json-simple:json-simple:1.1.1')

    implementation(group: 'org.slf4j', name: 'slf4j-simple', version: '1.7.31')
    testImplementation(group: 'junit', name: 'junit', version: '4.12')
}